/** @type {import('next').NextConfig} */
const nextConfig = {
  swcMinify: true,
  reactStrictMode: false,
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**.slack-edge.com",
      },
      {
        protocol: "http",
        hostname: "bot-designer-server.awdfaydwabfah9f5.uaenorth.azurecontainer.io",
        port: "5003",
      },
      {
        protocol: "http",
        hostname: "i2i-rag.dubmafhwd3ekana6.uaenorth.azurecontainer.io",
        port: "8080",
      },
    ],
  },
  // Headers to allow HTTP requests in production
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: process.env.NODE_ENV === 'production'
              ? "connect-src 'self' http://bot-designer-server.awdfaydwabfah9f5.uaenorth.azurecontainer.io:5003 http://i2i-rag.dubmafhwd3ekana6.uaenorth.azurecontainer.io:8080 http://**************:8000 https: wss: ws: data:; img-src 'self' data: https: http:; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; font-src 'self' data: https:; object-src 'none';"
              : ""
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
