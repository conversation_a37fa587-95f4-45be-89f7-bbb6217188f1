/** @type {import('next').NextConfig} */
const nextConfig = {
  swcMinify: true,
  reactStrictMode: false,
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**.slack-edge.com",
      },
      {
        protocol: "http",
        hostname: "bot-designer-server.awdfaydwabfah9f5.uaenorth.azurecontainer.io",
        port: "5003",
      },
      {
        protocol: "http",
        hostname: "i2i-rag.dubmafhwd3ekana6.uaenorth.azurecontainer.io",
        port: "8080",
      },
    ],
  },
  // Remove CSP headers that block mixed content
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
