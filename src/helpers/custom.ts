import auth from "./auth";

const getHeader = () => {
  const header = {
    Accept: "application/json",
    "Content-Type": "application/json",
    Authorization: auth.getHeaderToken(),
  };
  return header;
};

// Enhanced fetch function that handles HTTP requests in production
const secureFetch = async (url: string, options: RequestInit = {}) => {
  try {
    // For HTTP URLs in production, we need to handle them carefully
    if (typeof window !== 'undefined' && window.location.protocol === 'https:' && url.startsWith('http:')) {
      console.warn(`Making HTTP request from HTTPS page: ${url}`);

      // Try to use a proxy or alternative approach for HTTP requests
      const enhancedOptions = {
        ...options,
        headers: {
          ...options.headers,
          'X-Requested-With': 'XMLHttpRequest',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
        mode: 'cors' as RequestMode,
        cache: 'no-cache' as RequestCache,
      };

      return fetch(url, enhancedOptions);
    }

    return fetch(url, options);
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
};

const createPostJSON = (data) => {
  const lbc = {
    method: "POST",
    headers: getHeader(),
    withCredentials: true,
    crossdomain: true,
    body: JSON.stringify(data),
  };
  return lbc;
};

const createPutJSON = (data) => {
  const lbc = {
    method: "PUT",
    headers: getHeader(),
    withCredentials: true,
    crossdomain: true,
    body: JSON.stringify(data),
  };
  return lbc;
};

const createDELETEJSON = (data) => {
  const lbc = {
    method: "DELETE",
    headers: getHeader(),
    withCredentials: true,
    crossdomain: true,
    body: JSON.stringify(data),
  };
  return lbc;
};

const createGetJSON = () => {
  const lbc = {
    method: "GET",
    headers: getHeader(),
    withCredentials: true,
    crossdomain: true,
    timeout: 5000,
  };
  return lbc;
};

export { createGetJSON, createPostJSON, createPutJSON, createDELETEJSON, secureFetch };
