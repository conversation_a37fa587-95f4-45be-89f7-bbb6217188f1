import { NextApiRequest, NextApiResponse } from 'next';

const ACTUAL_SERVER_URL = "http://bot-designer-server.awdfaydwabfah9f5.uaenorth.azurecontainer.io:5003/api";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    return res.status(200).end();
  }

  try {
    // Extract the API path from the request
    let apiPath = '';
    let requestData = {};

    if (req.method === 'GET') {
      apiPath = req.query.path as string || '';
      // For GET requests, preserve any existing query parameters in the original URL
      const { path, ...otherParams } = req.query;
      if (Object.keys(otherParams).length > 0) {
        const queryString = new URLSearchParams(otherParams as Record<string, string>).toString();
        apiPath += (apiPath.includes('?') ? '&' : '?') + queryString;
      }
    } else {
      const body = req.body || {};
      apiPath = body.path || '';
      requestData = { ...body };
      delete (requestData as any).path;
    }

    // Construct the full URL
    const fullUrl = `${ACTUAL_SERVER_URL}${apiPath}`;

    console.log(`Proxying ${req.method} request to: ${fullUrl}`);

    // Forward the request to the actual server
    const response = await fetch(fullUrl, {
      method: req.method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        // Forward authorization header if present
        ...(req.headers.authorization && { 'Authorization': req.headers.authorization }),
      },
      // Only add body for non-GET requests
      ...(req.method !== 'GET' && Object.keys(requestData).length > 0 && {
        body: JSON.stringify(requestData)
      }),
    });

    const data = await response.json();

    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    res.status(response.status).json(data);
  } catch (error) {
    console.error('Proxy error:', error);
    res.status(500).json({ error: 'Proxy request failed', details: error.message });
  }
}
