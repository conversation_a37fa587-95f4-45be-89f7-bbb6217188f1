import { Html, Head, <PERSON>, NextScript } from "next/document";

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        {/* Allow mixed content (HTTP requests from HTTPS pages) */}
        <meta httpEquiv="Content-Security-Policy" content="upgrade-insecure-requests" />
        {/* Alternative approach - allow all HTTP connections */}
        <meta httpEquiv="Content-Security-Policy" content="connect-src 'self' http: https: ws: wss:; img-src 'self' data: http: https:; script-src 'self' 'unsafe-eval' 'unsafe-inline' http: https:; style-src 'self' 'unsafe-inline' http: https:;" />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
