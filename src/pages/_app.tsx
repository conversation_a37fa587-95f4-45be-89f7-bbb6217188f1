import "tailwindcss/tailwind.css";
import "styles/index.css";
import type { AppProps } from "next/app";
import LoadingBar from "react-top-loading-bar";
import LoaderProvider, { useLoaderContext } from "context/loaderContext";
import ClientOnly from "common/components/ClientOnly";
import { useEffect } from "react";

// Intercept fetch requests to handle HTTP URLs in production
const setupFetchInterceptor = () => {
  if (typeof window !== 'undefined') {
    const originalFetch = window.fetch;

    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.toString();

      // Check if this is an HTTP request to our server in production HTTPS
      if (window.location.protocol === 'https:' &&
          url.startsWith('http://bot-designer-server.awdfaydwabfah9f5.uaenorth.azurecontainer.io:5003/api')) {

        // Extract the API path
        const apiPath = url.replace('http://bot-designer-server.awdfaydwabfah9f5.uaenorth.azurecontainer.io:5003/api', '');
        const method = init?.method || 'GET';

        // Handle GET requests differently (no body allowed)
        if (method === 'GET') {
          // Parse the original URL to extract query parameters
          const urlObj = new URL(url);
          const queryParams = new URLSearchParams();

          // Add the path parameter
          queryParams.set('path', apiPath);

          // Add any existing query parameters from the original URL
          urlObj.searchParams.forEach((value, key) => {
            queryParams.set(key, value);
          });

          return originalFetch(`/api/proxy?${queryParams.toString()}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              ...init?.headers,
            },
          });
        } else {
          // For POST, PUT, DELETE requests
          return originalFetch('/api/proxy', {
            ...init,
            method: method,
            headers: {
              'Content-Type': 'application/json',
              ...init?.headers,
            },
            body: init?.body ? JSON.stringify({
              path: apiPath,
              ...JSON.parse(init.body as string || '{}')
            }) : JSON.stringify({ path: apiPath })
          });
        }
      }

      // For all other requests, use original fetch
      return originalFetch(input, init);
    };
  }
};

export default function App({ Component, pageProps }: AppProps) {
  useEffect(() => {
    setupFetchInterceptor();
  }, []);

  return (
    <>
      <LoaderProvider>
          <Component {...pageProps} />
      </LoaderProvider>
    </>
  );
}
