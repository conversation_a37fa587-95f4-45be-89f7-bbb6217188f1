import "tailwindcss/tailwind.css";
import "styles/index.css";
import type { AppProps } from "next/app";
import LoadingBar from "react-top-loading-bar";
import LoaderProvider, { useLoaderContext } from "context/loaderContext";
import ClientOnly from "common/components/ClientOnly";
import { useEffect } from "react";

// Intercept fetch requests to handle HTTP URLs in production
const setupFetchInterceptor = () => {
  if (typeof window !== 'undefined') {
    const originalFetch = window.fetch;

    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.toString();

      // Check if this is an HTTP request to our server in production HTTPS
      if (window.location.protocol === 'https:' &&
          url.startsWith('http://bot-designer-server.awdfaydwabfah9f5.uaenorth.azurecontainer.io:5003/api')) {

        // Extract the API path
        const apiPath = url.replace('http://bot-designer-server.awdfaydwabfah9f5.uaenorth.azurecontainer.io:5003/api', '');

        // Use our proxy instead
        return originalFetch('/api/proxy', {
          ...init,
          method: init?.method || 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...init?.headers,
          },
          body: init?.body ? JSON.stringify({
            path: apiPath,
            ...JSON.parse(init.body as string || '{}')
          }) : JSON.stringify({ path: apiPath })
        });
      }

      // For all other requests, use original fetch
      return originalFetch(input, init);
    };
  }
};

export default function App({ Component, pageProps }: AppProps) {
  useEffect(() => {
    setupFetchInterceptor();
  }, []);

  return (
    <>
      <LoaderProvider>
          <Component {...pageProps} />
      </LoaderProvider>
    </>
  );
}
